#!/usr/bin/env tsx

/**
 * Error handling test runner for bestWordsFast function
 */

import { runErrorHandlingTests } from '../src/lib/tests/bestWordsFast.errorHandling.test';

async function main() {
	console.log('🚀 Starting bestWordsFast Error Handling Test Suite');
	console.log('===============================================\n');
	
	try {
		const results = runErrorHandlingTests();
		
		const failedTests = results.filter(r => !r.passed);
		
		if (failedTests.length === 0) {
			console.log('🎉 All error handling tests passed!');
			console.log('\n✅ bestWordsFast handles errors and edge cases gracefully');
			process.exit(0);
		} else {
			console.log('\n❌ Some error handling tests failed:');
			failedTests.forEach(test => {
				console.log(`   - ${test.name}: ${test.error}`);
			});
			console.log('\n⚠️  Error handling issues detected - review failed tests');
			process.exit(1);
		}
	} catch (error) {
		console.error('💥 Error handling test suite crashed:', error);
		process.exit(1);
	}
}

main();
