#!/usr/bin/env tsx

/**
 * Comprehensive test runner for all bestWordsFast tests
 */

import { runBestWordsFastTests } from '../src/lib/tests/bestWordsFast.test';
import { runIntegrationTests } from '../src/lib/tests/bestWordsFast.integration.test';
import { runErrorHandlingTests } from '../src/lib/tests/bestWordsFast.errorHandling.test';

interface TestSuiteResult {
	name: string;
	passed: number;
	total: number;
	duration: number;
	details: any[];
}

async function runTestSuite(name: string, testRunner: () => any[]): Promise<TestSuiteResult> {
	console.log(`\n🧪 Running ${name}...`);
	console.log('='.repeat(50));
	
	const startTime = performance.now();
	const results = testRunner();
	const endTime = performance.now();
	
	const passed = results.filter(r => r.passed).length;
	const total = results.length;
	const duration = endTime - startTime;
	
	console.log(`\n📊 ${name} Results: ${passed}/${total} tests passed (${duration.toFixed(1)}ms)`);
	
	return {
		name,
		passed,
		total,
		duration,
		details: results
	};
}

async function main() {
	console.log('🚀 Starting Comprehensive bestWordsFast Test Suite');
	console.log('==================================================');
	console.log('This will run all tests for the bestWordsFast function:');
	console.log('• Core functionality tests');
	console.log('• Integration tests');
	console.log('• Error handling tests');
	console.log('• Performance analysis');
	console.log();
	
	const suiteResults: TestSuiteResult[] = [];
	let totalPassed = 0;
	let totalTests = 0;
	let totalDuration = 0;
	
	try {
		// Run core functionality tests
		const coreResults = await runTestSuite('Core Functionality Tests', runBestWordsFastTests);
		suiteResults.push(coreResults);
		
		// Run integration tests
		const integrationResults = await runTestSuite('Integration Tests', runIntegrationTests);
		suiteResults.push(integrationResults);
		
		// Run error handling tests
		const errorResults = await runTestSuite('Error Handling Tests', runErrorHandlingTests);
		suiteResults.push(errorResults);
		
		// Calculate totals
		for (const suite of suiteResults) {
			totalPassed += suite.passed;
			totalTests += suite.total;
			totalDuration += suite.duration;
		}
		
		// Print comprehensive summary
		console.log('\n' + '='.repeat(60));
		console.log('📋 COMPREHENSIVE TEST SUMMARY');
		console.log('='.repeat(60));
		
		suiteResults.forEach(suite => {
			const status = suite.passed === suite.total ? '✅' : '❌';
			const percentage = ((suite.passed / suite.total) * 100).toFixed(1);
			console.log(`${status} ${suite.name}: ${suite.passed}/${suite.total} (${percentage}%) - ${suite.duration.toFixed(1)}ms`);
		});
		
		console.log('─'.repeat(60));
		const overallPercentage = ((totalPassed / totalTests) * 100).toFixed(1);
		console.log(`📊 OVERALL: ${totalPassed}/${totalTests} tests passed (${overallPercentage}%)`);
		console.log(`⏱️  Total execution time: ${totalDuration.toFixed(1)}ms`);
		
		// Detailed failure analysis
		const failedSuites = suiteResults.filter(s => s.passed < s.total);
		if (failedSuites.length > 0) {
			console.log('\n❌ FAILED TESTS ANALYSIS:');
			failedSuites.forEach(suite => {
				console.log(`\n${suite.name}:`);
				const failedTests = suite.details.filter(t => !t.passed);
				failedTests.forEach(test => {
					console.log(`   • ${test.name}: ${test.error}`);
				});
			});
		}
		
		// Performance insights
		console.log('\n📈 PERFORMANCE INSIGHTS:');
		const avgTestTime = totalDuration / totalTests;
		console.log(`• Average test execution time: ${avgTestTime.toFixed(2)}ms`);
		console.log(`• Fastest test suite: ${suiteResults.reduce((min, s) => s.duration < min.duration ? s : min).name}`);
		console.log(`• Slowest test suite: ${suiteResults.reduce((max, s) => s.duration > max.duration ? s : max).name}`);
		
		// Quality assessment
		console.log('\n🎯 QUALITY ASSESSMENT:');
		if (totalPassed === totalTests) {
			console.log('🌟 EXCELLENT: All tests pass! The bestWordsFast function is robust and reliable.');
			console.log('✅ Ready for production use');
			console.log('✅ Handles all edge cases gracefully');
			console.log('✅ Integrates properly with the game system');
			console.log('✅ Performance is optimal');
		} else if (overallPercentage >= 90) {
			console.log('🟡 GOOD: Most tests pass, but some issues need attention.');
			console.log('⚠️  Review failed tests before production deployment');
		} else if (overallPercentage >= 75) {
			console.log('🟠 FAIR: Significant issues detected that need fixing.');
			console.log('❌ Not recommended for production use');
		} else {
			console.log('🔴 POOR: Major issues detected. Extensive fixes needed.');
			console.log('❌ Do not use in production');
		}
		
		// Recommendations
		console.log('\n💡 RECOMMENDATIONS:');
		if (totalPassed === totalTests) {
			console.log('• Consider adding more edge case tests as the codebase evolves');
			console.log('• Monitor performance in production environments');
			console.log('• Set up automated testing in CI/CD pipeline');
		} else {
			console.log('• Fix all failing tests before deployment');
			console.log('• Add additional tests for any new edge cases discovered');
			console.log('• Consider code review for error handling improvements');
		}
		
		// Exit with appropriate code
		if (totalPassed === totalTests) {
			console.log('\n🎉 All tests passed! bestWordsFast is ready for use.');
			process.exit(0);
		} else {
			console.log(`\n⚠️  ${totalTests - totalPassed} tests failed. Please review and fix issues.`);
			process.exit(1);
		}
		
	} catch (error) {
		console.error('\n💥 Test suite execution failed:', error);
		console.log('\n🔧 This indicates a serious issue with the test infrastructure or function.');
		console.log('Please check:');
		console.log('• Dictionary file exists and is accessible');
		console.log('• All dependencies are properly installed');
		console.log('• No syntax errors in the code');
		process.exit(1);
	}
}

main();
