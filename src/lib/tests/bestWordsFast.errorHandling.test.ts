/**
 * Error handling and edge case tests for bestWordsFast function
 */

import { Board } from '../models/Board';
import { Tile } from '../models/Tile';
import { bestWordsFast } from '../bestWordsFast';

interface ErrorTestResult {
	name: string;
	passed: boolean;
	error?: string;
	details?: any;
}

/**
 * Test input validation
 */
function testInputValidation(): ErrorTestResult {
	try {
		const validBoard = Board.createRandom();
		
		// Test null/undefined board
		try {
			bestWordsFast(null as any, 10);
			return { name: 'Input Validation', passed: false, error: 'Should throw for null board' };
		} catch (error) {
			if (!String(error).includes('board parameter is required')) {
				return { name: 'Input Validation', passed: false, error: `Wrong error for null board: ${error}` };
			}
		}
		
		// Test invalid K values
		const invalidKValues = [-1, -10, 1.5, NaN, Infinity, 'invalid' as any];
		
		for (const k of invalidKValues) {
			try {
				bestWordsFast(validBoard, k);
				return { name: 'Input Validation', passed: false, error: `Should throw for K=${k}` };
			} catch (error) {
				if (!String(error).includes('K must be')) {
					return { name: 'Input Validation', passed: false, error: `Wrong error for K=${k}: ${error}` };
				}
			}
		}
		
		// Test valid edge cases
		const validResults0 = bestWordsFast(validBoard, 0);
		if (validResults0.length !== 0) {
			return { name: 'Input Validation', passed: false, error: 'K=0 should return empty array' };
		}
		
		const validResults1 = bestWordsFast(validBoard, 1);
		if (validResults1.length > 1) {
			return { name: 'Input Validation', passed: false, error: 'K=1 should return at most 1 result' };
		}
		
		return {
			name: 'Input Validation',
			passed: true,
			details: { validKValuesChecked: [0, 1], invalidKValuesChecked: invalidKValues.length }
		};
	} catch (error) {
		return { name: 'Input Validation', passed: false, error: String(error) };
	}
}

/**
 * Test board structure validation
 */
function testBoardValidation(): ErrorTestResult {
	try {
		// Test invalid board structures
		const invalidBoards = [
			{ name: 'missing tiles', board: { tiles: null } },
			{ name: 'wrong size', board: { tiles: [[]] } },
			{ name: 'wrong row size', board: { tiles: Array(5).fill([]) } },
		];
		
		for (const { name, board } of invalidBoards) {
			try {
				bestWordsFast(board as any, 10);
				return { name: 'Board Validation', passed: false, error: `Should throw for ${name}` };
			} catch (error) {
				if (!String(error).includes('board')) {
					return { name: 'Board Validation', passed: false, error: `Wrong error for ${name}: ${error}` };
				}
			}
		}
		
		// Test board with invalid tiles
		const invalidTileBoard = {
			tiles: Array(5).fill(null).map(() => 
				Array(5).fill(null).map(() => ({ letter: null, row: 0, col: 0, letterMult: 1, wordMult: 1 }))
			)
		};
		
		try {
			bestWordsFast(invalidTileBoard as any, 10);
			return { name: 'Board Validation', passed: false, error: 'Should throw for invalid tiles' };
		} catch (error) {
			if (!String(error).includes('invalid tile')) {
				return { name: 'Board Validation', passed: false, error: `Wrong error for invalid tiles: ${error}` };
			}
		}
		
		return {
			name: 'Board Validation',
			passed: true,
			details: { invalidBoardsChecked: invalidBoards.length + 1 }
		};
	} catch (error) {
		return { name: 'Board Validation', passed: false, error: String(error) };
	}
}

/**
 * Test extreme values and edge cases
 */
function testExtremeValues(): ErrorTestResult {
	try {
		const board = Board.createRandom();
		
		// Test very large K values
		const largeKResults = bestWordsFast(board, 1000000);
		if (!Array.isArray(largeKResults)) {
			return { name: 'Extreme Values', passed: false, error: 'Should return array for large K' };
		}
		
		// Test with board containing unusual letters (if any)
		const specialBoard = createSpecialBoard();
		const specialResults = bestWordsFast(specialBoard, 10);
		if (!Array.isArray(specialResults)) {
			return { name: 'Extreme Values', passed: false, error: 'Should handle special board' };
		}
		
		// Test performance with large K
		const startTime = performance.now();
		const performanceResults = bestWordsFast(board, 500);
		const endTime = performance.now();
		
		const executionTime = endTime - startTime;
		if (executionTime > 5000) { // 5 second timeout
			return { 
				name: 'Extreme Values', 
				passed: false, 
				error: `Too slow for K=500: ${executionTime}ms` 
			};
		}
		
		return {
			name: 'Extreme Values',
			passed: true,
			details: {
				largeKResults: largeKResults.length,
				specialBoardResults: specialResults.length,
				performanceTime: Math.round(executionTime),
				performanceResults: performanceResults.length
			}
		};
	} catch (error) {
		return { name: 'Extreme Values', passed: false, error: String(error) };
	}
}

/**
 * Test memory and resource management
 */
function testResourceManagement(): ErrorTestResult {
	try {
		const board = Board.createRandom();
		
		// Test multiple consecutive calls
		const results = [];
		for (let i = 0; i < 10; i++) {
			const words = bestWordsFast(board, 25);
			results.push(words.length);
		}
		
		// Results should be consistent
		const firstResult = results[0];
		const allSame = results.every(r => r === firstResult);
		if (!allSame) {
			return {
				name: 'Resource Management',
				passed: false,
				error: `Inconsistent results across calls: ${results.join(', ')}`
			};
		}
		
		// Test with different boards rapidly
		const rapidResults = [];
		for (let i = 0; i < 5; i++) {
			const testBoard = Board.createRandom();
			const words = bestWordsFast(testBoard, 10);
			rapidResults.push(words.length);
		}
		
		// Should not crash or hang
		return {
			name: 'Resource Management',
			passed: true,
			details: {
				consecutiveCallResults: results,
				rapidCallResults: rapidResults,
				memoryStable: true
			}
		};
	} catch (error) {
		return { name: 'Resource Management', passed: false, error: String(error) };
	}
}

/**
 * Test graceful degradation
 */
function testGracefulDegradation(): ErrorTestResult {
	try {
		// Test with board that might have limited word possibilities
		const limitedBoard = createLimitedBoard();
		const limitedResults = bestWordsFast(limitedBoard, 100);
		
		// Should not crash even if few words are possible
		if (!Array.isArray(limitedResults)) {
			return { name: 'Graceful Degradation', passed: false, error: 'Should return array for limited board' };
		}
		
		// Test with board containing repeated letters
		const repeatedBoard = createRepeatedLetterBoard();
		const repeatedResults = bestWordsFast(repeatedBoard, 50);
		
		if (!Array.isArray(repeatedResults)) {
			return { name: 'Graceful Degradation', passed: false, error: 'Should handle repeated letters' };
		}
		
		return {
			name: 'Graceful Degradation',
			passed: true,
			details: {
				limitedBoardResults: limitedResults.length,
				repeatedBoardResults: repeatedResults.length,
				gracefulHandling: true
			}
		};
	} catch (error) {
		return { name: 'Graceful Degradation', passed: false, error: String(error) };
	}
}

/**
 * Helper function to create a board with special characteristics
 */
function createSpecialBoard(): Board {
	const tiles: Tile[][] = [];
	
	// Board with less common letters
	const letters = [
		['Q', 'X', 'Z', 'J', 'K'],
		['V', 'W', 'Y', 'F', 'H'],
		['P', 'B', 'C', 'M', 'G'],
		['D', 'L', 'N', 'R', 'S'],
		['T', 'A', 'E', 'I', 'O']
	];
	
	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			tiles[row][col] = new Tile(
				letters[row][col],
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				1,
				1
			);
		}
	}
	
	return new Board(tiles);
}

/**
 * Helper function to create a board with limited word possibilities
 */
function createLimitedBoard(): Board {
	const tiles: Tile[][] = [];
	
	// Board with mostly uncommon letters
	const letters = [
		['Z', 'Z', 'Z', 'Z', 'Z'],
		['X', 'X', 'X', 'X', 'X'],
		['Q', 'Q', 'Q', 'Q', 'Q'],
		['J', 'J', 'J', 'J', 'J'],
		['A', 'E', 'I', 'O', 'U'] // Some vowels to make a few words possible
	];
	
	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			tiles[row][col] = new Tile(
				letters[row][col],
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				1,
				1
			);
		}
	}
	
	return new Board(tiles);
}

/**
 * Helper function to create a board with repeated letters
 */
function createRepeatedLetterBoard(): Board {
	const tiles: Tile[][] = [];
	
	// Board with many repeated letters
	const letters = [
		['A', 'A', 'A', 'A', 'A'],
		['E', 'E', 'E', 'E', 'E'],
		['T', 'T', 'T', 'T', 'T'],
		['S', 'S', 'S', 'S', 'S'],
		['R', 'R', 'R', 'R', 'R']
	];
	
	for (let row = 0; row < 5; row++) {
		tiles[row] = [];
		for (let col = 0; col < 5; col++) {
			tiles[row][col] = new Tile(
				letters[row][col],
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				1,
				1
			);
		}
	}
	
	return new Board(tiles);
}

/**
 * Run all error handling tests
 */
export function runErrorHandlingTests(): ErrorTestResult[] {
	console.log('🛡️ Running bestWordsFast error handling tests...\n');
	
	const tests = [
		testInputValidation,
		testBoardValidation,
		testExtremeValues,
		testResourceManagement,
		testGracefulDegradation
	];
	
	const results: ErrorTestResult[] = [];
	
	for (const test of tests) {
		const testName = test.name || 'Unknown Test';
		console.log(`Running ${testName}...`);
		
		try {
			const result = test();
			results.push(result);

			if (result.passed) {
				console.log(`✅ ${result.name} passed`);
			} else {
				console.log(`❌ ${result.name} failed: ${result.error}`);
			}

			if (result.details) {
				console.log(`   Details:`, result.details);
			}
		} catch (error) {
			console.log(`❌ ${testName} crashed: ${error}`);
			results.push({
				name: testName,
				passed: false,
				error: String(error)
			});
		}
		console.log();
	}
	
	const passedCount = results.filter(r => r.passed).length;
	console.log(`\n📊 Error Handling Test Results: ${passedCount}/${results.length} tests passed`);
	
	return results;
}

// Export individual test functions for external use
export {
	testInputValidation,
	testBoardValidation,
	testExtremeValues,
	testResourceManagement,
	testGracefulDegradation
};
