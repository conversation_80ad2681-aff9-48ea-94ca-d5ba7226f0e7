/**
 * Ultra-fast version of bestWords function for the Letters game
 *
 * This function finds the best scoring words that can be formed on a given board
 * by optimizing the tile selection process to prioritize multipliers.
 *
 * Key optimizations:
 * 1. Hybrid dictionary processing: simple words first, then high-scoring words
 * 2. Greedy assignment for fast approximation of tile placement
 * 3. Hungarian algorithm only for final top candidates
 * 4. Early termination with score bounds for performance
 * 5. Multiplier-aware tile selection for optimal scoring
 *
 * Algorithm Overview:
 * - Phase 1: Fast filtering using greedy scoring to find candidate words
 * - Phase 2: Precise scoring using Hungarian algorithm for top candidates
 * - Phase 3: Position finding that prioritizes tiles with multipliers
 *
 * @param board - The 5x5 game board with tiles and multipliers
 * @param K - Maximum number of words to return
 * @returns Array of Word objects sorted by score (highest first)
 *
 * @example
 * ```typescript
 * const board = Board.createRandom();
 * const topWords = bestWordsFast(board, 10);
 * console.log(`Best word: ${topWords[0].letters} (${topWords[0].score} points)`);
 * ```
 */

import { readFileSync } from 'node:fs';
import { Tile } from './models/Tile';
import { Board } from './models/Board';
import { Word } from './models/Word';
import { solveAssignment } from './solver/hungarian';
import { join } from 'path';

const DICT_BIN = join(process.cwd(), 'src/lib/dict.bin');
const dictBytes = readFileSync(DICT_BIN);
const dv = new DataView(dictBytes.buffer, dictBytes.byteOffset);
const textDec = new TextDecoder();

// Letter scores
const LETTER_SCORES: number[] = Array(26).fill(0);
Object.assign(LETTER_SCORES, {
	0: 1,
	1: 4,
	2: 4,
	3: 2,
	4: 1,
	5: 4,
	6: 2,
	7: 4,
	8: 1,
	9: 10,
	10: 5,
	11: 1,
	12: 4,
	13: 2,
	14: 1,
	15: 3,
	16: 10,
	17: 1,
	18: 1,
	19: 1,
	20: 2,
	21: 5,
	22: 4,
	23: 8,
	24: 3,
	25: 10
});

/**
 * Metadata for a dictionary entry in the binary format
 */
interface DictEntryMeta {
	/** Byte offset in the dictionary file where the word text starts */
	off: number;
	/** Length of the word in bytes (UTF-8) */
	len: number;
	/** Sum of letter scores without multipliers */
	letterScoreSum: number;
	/** Length of the word in characters */
	wordLen: number;
	/** Letter frequency histogram (26 bytes for A-Z) */
	hist: Uint8Array;
}

let ENTRIES: DictEntryMeta[] | null = null;
let ENTRIES_BY_SCORE: DictEntryMeta[] | null = null;

function loadDictionary(): DictEntryMeta[] {
	if (ENTRIES) return ENTRIES;

	try {
		let o = 0;

		// Validate dictionary file size
		if (dictBytes.length < 4) {
			throw new Error('Dictionary file is too small (missing header)');
		}

		const nEntries = dv.getUint32(o, true);
		o += 4;

		// Validate entry count
		if (nEntries <= 0 || nEntries > 1000000) {
			throw new Error(`Invalid dictionary entry count: ${nEntries}`);
		}

		const entries: DictEntryMeta[] = new Array(nEntries);

		for (let idx = 0; idx < nEntries; idx++) {
			// Check bounds before reading
			if (o + 31 > dictBytes.length) {
				throw new Error(`Dictionary corruption: insufficient data for entry ${idx}`);
			}

			const wordBytes = dv.getUint16(o, true);
			o += 2;
			const score = dv.getUint16(o, true);
			o += 2;
			const wlen = dv.getUint8(o);
			o += 1;

			// Validate word length
			if (wlen <= 0 || wlen > 50) {
				throw new Error(`Invalid word length ${wlen} for entry ${idx}`);
			}

			if (wordBytes <= 0 || wordBytes > 200) {
				throw new Error(`Invalid word byte length ${wordBytes} for entry ${idx}`);
			}

			const hist = new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + o, 26);
			o += 26;

			// Check bounds for word data
			if (o + wordBytes > dictBytes.length) {
				throw new Error(`Dictionary corruption: insufficient data for word ${idx}`);
			}

			entries[idx] = { off: o, len: wordBytes, letterScoreSum: score, wordLen: wlen, hist };
			o += wordBytes;
		}

		ENTRIES = entries;
		return entries;
	} catch (error) {
		// Reset cached entries on error
		ENTRIES = null;
		ENTRIES_BY_SCORE = null;
		throw new Error(`Failed to load dictionary: ${error}`);
	}
}

function getEntriesByScore(): DictEntryMeta[] {
	if (ENTRIES_BY_SCORE) return ENTRIES_BY_SCORE;

	const entries = loadDictionary();
	ENTRIES_BY_SCORE = [...entries].sort((a, b) => b.letterScoreSum - a.letterScoreSum);
	return ENTRIES_BY_SCORE;
}

/**
 * Summarized board state for efficient word finding
 */
interface LetterBag {
	/** Letter frequency count for each letter A-Z */
	freq: Uint8Array;
	/** All tiles grouped by letter, sorted by multiplier value (best first) */
	tiles: Tile[][];
	/** Best tile for each letter (highest multiplier value) */
	bestTiles: Tile[];
	/** Maximum multiplier values found on the board */
	maxMultipliers: { letter: number; word: number };
}

/**
 * Analyzes the board and creates a summary for efficient word finding
 *
 * @param board - The game board to analyze
 * @returns LetterBag containing letter frequencies, tiles grouped by letter, and multiplier info
 */
function summariseBoard(board: Board): LetterBag {
	const freq = new Uint8Array(26);
	const tiles: Tile[][] = Array.from({ length: 26 }, () => []);
	const bestTiles: Tile[] = new Array(26);
	let maxLetterMult = 1;
	let maxWordMult = 1;

	for (const row of board.tiles) {
		for (const t of row) {
			const idx = t.letter.charCodeAt(0) - 65;
			freq[idx]++;
			tiles[idx].push(t);

			// Track best tile for each letter
			if (
				!bestTiles[idx] ||
				t.letterMult * t.wordMult > bestTiles[idx].letterMult * bestTiles[idx].wordMult
			) {
				bestTiles[idx] = t;
			}

			maxLetterMult = Math.max(maxLetterMult, t.letterMult);
			maxWordMult = Math.max(maxWordMult, t.wordMult);
		}
	}

	// Sort tiles by multiplier value (best first) for each letter
	for (let i = 0; i < 26; i++) {
		if (tiles[i].length > 1) {
			tiles[i].sort((a, b) => {
				const aValue = a.letterMult * a.wordMult;
				const bValue = b.letterMult * b.wordMult;
				return bValue - aValue; // Descending order (best first)
			});
		}
	}

	return { freq, tiles, bestTiles, maxMultipliers: { letter: maxLetterMult, word: maxWordMult } };
}

// Ultra-fast greedy scoring (no Hungarian algorithm)
/**
 * Calculate an approximate score for a word using greedy tile assignment
 *
 * This is faster than the Hungarian algorithm but less accurate.
 * Used for initial filtering of candidate words.
 *
 * @param word - The word to score
 * @param bag - Board summary with tile information
 * @returns Approximate score for the word
 */
function greedyPlacementScore(word: string, bag: LetterBag): number {
	let totalScore = 0;
	let wordMultiplier = 1;
	const letterCounts: Record<string, number> = {};

	for (const ch of word) {
		const letterIdx = ch.charCodeAt(0) - 65;
		letterCounts[ch] = (letterCounts[ch] || 0) + 1;

		const availableTiles = bag.tiles[letterIdx];
		if (availableTiles.length >= letterCounts[ch]) {
			// Use the best available tile for this letter occurrence
			// Since tiles are sorted by multiplier value (best first), use index based on count
			const tileIndex = letterCounts[ch] - 1;
			const tile = availableTiles[tileIndex];

			totalScore += LETTER_SCORES[letterIdx] * tile.letterMult;
			wordMultiplier *= tile.wordMult;
		} else {
			// Not enough tiles, use base score
			totalScore += LETTER_SCORES[letterIdx];
		}
	}

	return totalScore * wordMultiplier;
}

// Optimistic upper bound (maximum possible score)
function upperBoundScore(word: string, bag: LetterBag): number {
	let totalScore = 0;
	const letterCounts: Record<string, number> = {};

	for (const ch of word) {
		const letterIdx = ch.charCodeAt(0) - 65;
		letterCounts[ch] = (letterCounts[ch] || 0) + 1;

		// Use best possible tile for this letter
		const bestTile = bag.bestTiles[letterIdx];
		if (bestTile) {
			totalScore += LETTER_SCORES[letterIdx] * bestTile.letterMult;
		} else {
			totalScore += LETTER_SCORES[letterIdx];
		}
	}

	// Apply maximum word multiplier
	return totalScore * bag.maxMultipliers.word;
}

/**
 * Calculate the precise optimal score for a word using the Hungarian algorithm
 *
 * This finds the optimal assignment of letters to tiles to maximize score.
 * More accurate but slower than greedy scoring. Only used for final candidates.
 *
 * @param word - The word to score
 * @param tilesBag - Available tiles grouped by letter
 * @param letterScoreSum - Sum of base letter scores (optimization)
 * @returns Optimal score for the word
 */
function precisePlacementScore(word: string, tilesBag: Tile[][], letterScoreSum: number): number {
	const n = word.length;
	if (n > 12) return letterScoreSum; // Skip Hungarian for very long words

	const M: number[][] = Array.from({ length: n }, () => new Array(n).fill(999999));

	const letterCounts: Record<string, number> = {};
	for (let i = 0; i < n; i++) {
		const ch = word[i];
		letterCounts[ch] = (letterCounts[ch] ?? 0) + 1;
		const tileList = tilesBag[ch.charCodeAt(0) - 65];

		// Since tiles are now sorted by multiplier value (best first),
		// we can use them in order for the Hungarian algorithm
		let col = 0;
		for (let tIdx = 0; tIdx < Math.min(tileList.length, n); tIdx++) {
			const tile = tileList[tIdx];
			const letterValue = LETTER_SCORES[ch.charCodeAt(0) - 65] * tile.letterMult;
			const wordMult = tile.wordMult;
			M[i][col++] = -letterValue - (wordMult > 1 ? 1e4 * wordMult : 0);
		}
	}

	const result = solveAssignment(M);
	if (!result.success || result.assignment.length !== n) {
		return letterScoreSum;
	}

	let letterSum = 0,
		wordMultTotal = 1;
	for (let r = 0; r < n; r++) {
		const letterIdx = word[r].charCodeAt(0) - 65;
		const tileList = tilesBag[letterIdx];
		const assignedCol = result.assignment[r];

		if (assignedCol < 0 || assignedCol >= tileList.length) {
			letterSum += LETTER_SCORES[letterIdx];
			continue;
		}

		const tile = tileList[assignedCol];
		if (!tile) {
			letterSum += LETTER_SCORES[letterIdx];
			continue;
		}

		letterSum += LETTER_SCORES[letterIdx] * tile.letterMult;
		wordMultTotal *= tile.wordMult;
	}
	return letterSum * wordMultTotal;
}

/**
 * Find optimal positions for a word on the board prioritizing multipliers
 *
 * Note: In Letters game, tiles don't need to be adjacent - any tiles can be used
 *
 * @param word - The word to find positions for
 * @param board - The game board
 * @returns Array of [row, col] positions or null if word cannot be formed
 */
function findWordPositions(word: string, board: Board): Array<[number, number]> | null {
	const letters = word.split('');
	const positions: Array<[number, number]> = [];
	const usedPositions = new Set<string>();

	// Build a map of available tiles for each letter, sorted by multiplier value
	const letterTileMap: Record<string, Tile[]> = {};
	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 5; col++) {
			const tile = board.getTile(row, col);
			if (tile) {
				if (!letterTileMap[tile.letter]) {
					letterTileMap[tile.letter] = [];
				}
				letterTileMap[tile.letter].push(tile);
			}
		}
	}

	// Sort tiles by multiplier value (best first) for each letter
	for (const letter in letterTileMap) {
		letterTileMap[letter].sort((a, b) => {
			const aValue = a.letterMult * a.wordMult;
			const bValue = b.letterMult * b.wordMult;
			return bValue - aValue; // Descending order (best first)
		});
	}

	// For each letter in the word, find the best available tile
	for (const letter of letters) {
		const availableTiles = letterTileMap[letter];
		if (!availableTiles || availableTiles.length === 0) {
			return null; // Letter not available on board
		}

		// Find the best unused tile for this letter
		let bestTile: Tile | null = null;
		for (const tile of availableTiles) {
			const posKey = `${tile.row},${tile.col}`;
			if (!usedPositions.has(posKey)) {
				bestTile = tile;
				break; // Take the first (best) available tile
			}
		}

		if (!bestTile) {
			return null; // No unused tiles available for this letter
		}

		positions.push([bestTile.row, bestTile.col]);
		usedPositions.add(`${bestTile.row},${bestTile.col}`);
	}

	return positions;
}

// Ultra-fast main function
export function bestWordsFast(board: Board, K: number): Word[] {
	// Input validation
	if (!board) {
		throw new Error('bestWordsFast: board parameter is required');
	}

	if (typeof K !== 'number' || K < 0) {
		throw new Error('bestWordsFast: K must be a non-negative number');
	}

	if (!Number.isInteger(K)) {
		throw new Error('bestWordsFast: K must be an integer');
	}

	// Handle edge cases early
	if (K === 0) {
		return [];
	}

	// Validate board structure
	if (!board.tiles || !Array.isArray(board.tiles) || board.tiles.length !== 5) {
		throw new Error('bestWordsFast: board must have a 5x5 tiles array');
	}

	for (let row = 0; row < 5; row++) {
		if (!Array.isArray(board.tiles[row]) || board.tiles[row].length !== 5) {
			throw new Error(`bestWordsFast: board row ${row} must have 5 tiles`);
		}

		for (let col = 0; col < 5; col++) {
			const tile = board.tiles[row][col];
			if (!tile || typeof tile.letter !== 'string' || tile.letter.length !== 1) {
				throw new Error(`bestWordsFast: invalid tile at [${row}, ${col}]`);
			}
		}
	}

	let bag: LetterBag;
	try {
		bag = summariseBoard(board);
	} catch (error) {
		throw new Error(`bestWordsFast: failed to analyze board - ${error}`);
	}

	const freqB = bag.freq;

	// Phase 1: Fast filtering with greedy scoring
	const candidates: { word: string; score: number; meta: DictEntryMeta }[] = [];
	let minScore = 0;

	// Load dictionary with error handling
	let allEntries: DictEntryMeta[];
	let entriesByScore: DictEntryMeta[];

	try {
		allEntries = loadDictionary();
		entriesByScore = getEntriesByScore();
	} catch (error) {
		throw new Error(`bestWordsFast: dictionary loading failed - ${error}`);
	}

	if (!allEntries || allEntries.length === 0) {
		throw new Error('bestWordsFast: dictionary is empty');
	}

	// Use a hybrid approach: process some entries in original order, then by score
	const entries = [...allEntries.slice(0, 10000), ...entriesByScore.slice(0, 20000)];

	// Remove duplicates while preserving order
	const seenOffsets = new Set<number>();
	const uniqueEntries = entries.filter((entry) => {
		if (seenOffsets.has(entry.off)) return false;
		seenOffsets.add(entry.off);
		return true;
	});

	let processedCount = 0;
	const maxProcessed = Math.min(30000, uniqueEntries.length); // Limit processing for speed

	for (const meta of uniqueEntries) {
		if (processedCount++ > maxProcessed) break;

		// Quick length reject
		if (meta.wordLen > 25) continue;

		// Early termination if base score is too low (only for score-sorted entries)
		if (
			processedCount > 10000 &&
			meta.letterScoreSum * bag.maxMultipliers.letter * bag.maxMultipliers.word < minScore
		) {
			break; // Since later entries are sorted by score, we can stop here
		}

		// Histogram subset check
		let ok = true;
		for (let i = 0; i < 26; i++) {
			if (meta.hist[i] > freqB[i]) {
				ok = false;
				break;
			}
		}
		if (!ok) continue;

		// Decode word with error handling
		let word: string;
		try {
			word = textDec.decode(
				new Uint8Array(dictBytes.buffer, dictBytes.byteOffset + meta.off, meta.len)
			);
		} catch (error) {
			console.warn(`bestWordsFast: failed to decode word at offset ${meta.off}: ${error}`);
			continue;
		}

		// Validate decoded word
		if (!word || word.length === 0 || word.length !== meta.wordLen) {
			console.warn(
				`bestWordsFast: invalid decoded word: "${word}" (expected length: ${meta.wordLen})`
			);
			continue;
		}

		// Upper bound check with error handling
		let upperBound: number;
		try {
			upperBound = upperBoundScore(word, bag);
		} catch (error) {
			console.warn(`bestWordsFast: failed to calculate upper bound for "${word}": ${error}`);
			continue;
		}

		if (upperBound < minScore) continue;

		// Greedy scoring with error handling
		let greedyScore: number;
		try {
			greedyScore = greedyPlacementScore(word, bag);
		} catch (error) {
			console.warn(`bestWordsFast: failed to calculate greedy score for "${word}": ${error}`);
			continue;
		}

		if (greedyScore < minScore) continue;

		candidates.push({ word, score: greedyScore, meta });

		// Keep only top K candidates
		if (candidates.length > K * 2) {
			// Keep 2x for safety
			candidates.sort((a, b) => b.score - a.score);
			candidates.length = K;
			minScore = candidates[candidates.length - 1].score;
		}
	}

	// Sort candidates by greedy score
	candidates.sort((a, b) => b.score - a.score);
	const topCandidates = candidates.slice(0, Math.min(K * 2, candidates.length));

	// Phase 2: Precise scoring for top candidates only
	const finalResults: Word[] = [];

	for (const candidate of topCandidates) {
		try {
			// Find actual positions for the word on the board first
			const positions = findWordPositions(candidate.word, board);
			if (positions) {
				let preciseScore: number;
				try {
					preciseScore = precisePlacementScore(
						candidate.word,
						bag.tiles,
						candidate.meta.letterScoreSum
					);
				} catch (error) {
					console.warn(
						`bestWordsFast: failed to calculate precise score for "${candidate.word}": ${error}`
					);
					// Fall back to greedy score
					preciseScore = candidate.score;
				}

				// Create word with error handling
				try {
					const wordObj = new Word(candidate.word, positions, preciseScore);
					finalResults.push(wordObj);
				} catch (error) {
					console.warn(
						`bestWordsFast: failed to create Word object for "${candidate.word}": ${error}`
					);
					// Skip this word but continue processing
				}
			}
		} catch (error) {
			console.warn(`bestWordsFast: failed to process candidate "${candidate.word}": ${error}`);
			// Continue with next candidate
		}
	}

	// Final sort and trim with error handling
	try {
		finalResults.sort((a, b) => b.score - a.score);
	} catch (error) {
		console.warn(`bestWordsFast: failed to sort results: ${error}`);
		// Return unsorted results rather than failing completely
	}

	// Handle edge case where K=0 (should not reach here due to early return)
	if (K === 0) {
		return [];
	}

	// Validate final results
	const validResults = finalResults.filter((word) => {
		if (
			!word ||
			!word.letters ||
			typeof word.score !== 'number' ||
			!Array.isArray(word.positions)
		) {
			console.warn(`bestWordsFast: filtering out invalid word object:`, word);
			return false;
		}
		return true;
	});

	return validResults.slice(0, K);
}
